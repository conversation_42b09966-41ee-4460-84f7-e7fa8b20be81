# Maestro IDE Setup Guide

## Prerequisites

1. **Ring Programming Language**
   - Download and install Ring from: https://ring-lang.github.io/
   - Ensure `ring` command is available in your system PATH
   - Verify installation: `ring --version`

2. **Required Ring Libraries**
   - WebView library (for GUI)
   - LibCurl library (for HTTP requests)
   - JSON library (built-in with Ring)

## Installation Steps

### 1. Download Maestro IDE
```bash
# Clone or download the Maestro IDE files
# Ensure all files are in the correct directory structure
```

### 2. Configure API Keys
Edit the `config/api_keys.json` file and replace the placeholder API keys:

```json
{
    "default_provider": "gemini",
    "providers": {
        "gemini": {
            "api_key": "YOUR_ACTUAL_GEMINI_API_KEY",
            "enabled": true
        },
        "claude": {
            "api_key": "YOUR_ACTUAL_CLAUDE_API_KEY", 
            "enabled": false
        },
        "openrouter": {
            "api_key": "YOUR_ACTUAL_OPENROUTER_API_KEY",
            "enabled": false
        }
    }
}
```

### 3. Get API Keys

#### Google Gemini API
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to the config file

#### Anthropic Claude API
1. Go to [Anthropic Console](https://console.anthropic.com/)
2. Create an account and get API access
3. Generate an API key
4. Copy the key to the config file

#### OpenRouter API
1. Go to [OpenRouter](https://openrouter.ai/)
2. Create an account
3. Generate an API key
4. Copy the key to the config file

### 4. Verify Directory Structure
Ensure your directory structure looks like this:
```
Maestro/
├── main.ring                 # Entry point
├── README.md                 # Project documentation
├── SETUP.md                  # This setup guide
├── test_example.ring         # Test file
├── src/                      # Ring source files
│   ├── ring_ide.ring        # Main IDE class
│   ├── webview_handler.ring # WebView bridge
│   ├── http_client.ring     # HTTP client wrapper
│   ├── ai_client.ring       # Multi-provider AI client
│   ├── context_engine.ring  # Conversation context manager
│   ├── file_manager.ring    # File operations
│   └── code_runner.ring     # Code execution
├── config/                   # Configuration files
│   └── api_keys.json        # AI provider configurations
└── ui/                       # Frontend files
    └── index.html           # Complete web interface
```

## Running Maestro IDE

### 1. Basic Launch
```bash
cd /path/to/Maestro
ring main.ring
```

### 2. Troubleshooting

#### Common Issues:

**Error: WebView library not found**
- Install Ring WebView library
- On Windows: Ensure WebView2 runtime is installed
- On Linux: Install webkit2gtk development packages
- On macOS: WebKit should be available by default

**Error: LibCurl library not found**
- Install Ring LibCurl library
- On Windows: Ensure curl.dll is available
- On Linux: Install libcurl development packages
- On macOS: LibCurl should be available by default

**Error: File not found**
- Verify all source files are in the correct locations
- Check file permissions
- Ensure working directory is correct

**Error: API requests failing**
- Verify API keys are correctly configured
- Check internet connection
- Ensure API quotas are not exceeded
- Verify API endpoints are accessible

### 3. Testing the Installation

1. **Run the test file:**
   ```bash
   ring test_example.ring
   ```

2. **Launch the IDE:**
   ```bash
   ring main.ring
   ```

3. **Test basic functionality:**
   - Create a new file
   - Write some Ring code
   - Run the code
   - Test AI chat functionality

## Features Overview

### Code Editor
- Syntax highlighting for Ring language
- Code folding and auto-completion
- Line numbers and bracket matching
- Full-screen editing mode

### AI Assistant
- Multi-provider support (Gemini, Claude, OpenRouter)
- Code generation from comments (use `// gen`)
- Interactive chat for programming help
- Context-aware responses

### File Management
- Create, open, save, and delete files
- File explorer with recent files
- Auto-save functionality

### Code Execution
- Run Ring code directly in the IDE
- View output and error messages
- Execution history tracking

### Keyboard Shortcuts
- `Ctrl+N`: New file
- `Ctrl+O`: Open file
- `Ctrl+S`: Save file
- `Ctrl+R`: Run code
- `Ctrl+Enter`: Generate code from comment
- `Ctrl+T`: Toggle chat panel
- `F11`: Full-screen editor

## Configuration Options

### UI Settings
- Dark/Light theme toggle
- Font size and family customization
- Editor preferences

### AI Settings
- Provider selection
- Model parameters (temperature, max tokens)
- Context length settings

### Ring Settings
- Executable path configuration
- Default file extensions
- Auto-formatting options

## Support and Troubleshooting

### Log Files
- Check `maestro_errors.log` for error details
- Check `maestro_startup_errors.log` for startup issues

### Getting Help
1. Check the error logs
2. Verify API key configuration
3. Test with the provided example file
4. Ensure all dependencies are installed

### Performance Tips
- Close unused files to save memory
- Limit conversation history length
- Use appropriate AI model settings
- Keep the IDE updated

## Development and Customization

The IDE is built with a modular architecture:
- Backend: 100% Ring language
- Frontend: HTML/CSS/JavaScript with WebView
- Communication: WebView bind mechanism

You can extend the IDE by:
- Adding new AI providers
- Creating custom themes
- Implementing additional file operations
- Adding new keyboard shortcuts

## License

MIT License - See LICENSE file for details.
