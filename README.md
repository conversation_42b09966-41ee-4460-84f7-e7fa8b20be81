# Maestro - Ring Language IDE

Maestro is a state-of-the-art Integrated Development Environment (IDE) for the Ring programming language, featuring AI-powered code assistance and a modern web-based interface.

## Features

- **Modern Web-based Interface**: Built with Glassmorphism design and dark mode support
- **AI-Powered Code Assistant**: Multi-provider AI support (<PERSON>, <PERSON>, OpenRouter)
- **Advanced Code Editor**: CodeMirror-based editor with Ring language support
- **File Management**: Complete file operations (create, read, write, delete)
- **Code Execution**: Built-in Ring code runner with output display
- **Chat Interface**: Interactive AI chat with markdown rendering and code block actions

## Architecture

- **Backend**: 100% Ring language
- **Frontend**: Single HTML file with CSS and JavaScript
- **Communication**: WebView bind functionality for Ring-JavaScript bridge

## Project Structure

```
Maestro/
├── main.ring                 # Entry point
├── src/                      # Ring source files
│   ├── ring_ide.ring        # Main IDE class
│   ├── webview_handler.ring # WebView bridge
│   ├── http_client.ring     # HTTP client wrapper
│   ├── ai_client.ring       # Multi-provider AI client
│   ├── context_engine.ring  # Conversation context manager
│   ├── file_manager.ring    # File operations
│   └── code_runner.ring     # Code execution
├── config/                   # Configuration files
│   └── api_keys.json        # AI provider configurations
└── ui/                       # Frontend files
    └── index.html           # Complete web interface
```

## Setup

1. Install Ring programming language
2. Configure API keys in `config/api_keys.json`
3. Run `ring main.ring`

## Configuration

Edit `config/api_keys.json` to configure AI providers:

```json
{
    "default_provider": "gemini",
    "providers": {
        "gemini": { "api_key": "your_key", "endpoint": "...", "enabled": true },
        "claude": { "api_key": "your_key", "endpoint": "...", "enabled": false },
        "openrouter": { "api_key": "your_key", "endpoint": "...", "enabled": true }
    }
}
```

## License

MIT License
