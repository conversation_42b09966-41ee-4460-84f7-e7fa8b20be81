# HTTPClient Class - Robust wrapper around libcurl library
# Provides methods for HTTP POST/GET requests with headers, timeouts, and SSL verification

load "libcurl.ring"

class HTTPClient
    # Global variable to store response data
    cResponseData = ""
    
    # Constructor
    func init
        # Initialize default headers
        aDefaultHeaders = [
            "Content-Type: application/json",
            "Accept: application/json",
            "User-Agent: " + cUserAgent
        ]
    
    # Set timeout for requests
    func setTimeout nSeconds
        if nSeconds > 0
            nTimeout = nSeconds
        ok
    
    # Set SSL verification
    func setSSLVerify bVerify
        bSSLVerify = bVerify
    
    # Set custom user agent
    func setUserAgent cAgent
        cUserAgent = cAgent
        # Update default headers
        for i = 1 to len(aDefaultHeaders)
            if substr(aDefaultHeaders[i], "User-Agent:") = 1
                aDefaultHeaders[i] = "User-Agent: " + cUserAgent
                exit
            ok
        next
    
    # Add default header
    func addDefaultHeader cHeader
        aDefaultHeaders + cHeader
    
    # Clear default headers
    func clearDefaultHeaders
        aDefaultHeaders = []
    
    # Perform HTTP GET request
    func getRequest cURL, aCustomHeaders
        return performRequest("GET", cURL, "", aCustomHeaders)
    
    # Perform HTTP POST request
    func postRequest cURL, cData, aCustomHeaders
        return performRequest("POST", cURL, cData, aCustomHeaders)
    
    # Perform HTTP PUT request
    func putRequest cURL, cData, aCustomHeaders
        return performRequest("PUT", cURL, cData, aCustomHeaders)
    
    # Perform HTTP DELETE request
    func deleteRequest cURL, aCustomHeaders
        return performRequest("DELETE", cURL, "", aCustomHeaders)
    
    
    private
    # Private attributes
        nTimeout = 30           # Default timeout in seconds
        aDefaultHeaders = []    # Default headers for all requests
        bSSLVerify = true       # SSL verification flag
        cUserAgent = "Maestro-IDE/1.0"

    # Core method to perform HTTP requests

    # Perform HTTP request with specified method, URL, data, and headers
    func performRequest cMethod, cURL, cData, aCustomHeaders
        oResponse = [
            :success = false,
            :data = "",
            :status_code = 0,
            :headers = "",
            :error = ""
        ]
        
        try
            # Initialize curl
            pCurl = curl_easy_init()
            if pCurl = NULL
                oResponse[:error] = "Failed to initialize curl"
                return oResponse
            ok
            
            # Set URL
            curl_easy_setopt(pCurl, CURLOPT_URL, cURL)
            
            # Set timeout
            curl_easy_setopt(pCurl, CURLOPT_TIMEOUT, nTimeout)
            
            # Set SSL verification
            if not bSSLVerify
                curl_easy_setopt(pCurl, CURLOPT_SSL_VERIFYPEER, 0)
                curl_easy_setopt(pCurl, CURLOPT_SSL_VERIFYHOST, 0)
            ok
            
            # Prepare headers
            aAllHeaders = aDefaultHeaders
            if aCustomHeaders != NULL and len(aCustomHeaders) > 0
                for cHeader in aCustomHeaders
                    aAllHeaders + cHeader
                next
            ok
            
            # Set headers if any
            if len(aAllHeaders) > 0
                pHeaders = NULL
                for cHeader in aAllHeaders
                    pHeaders = curl_slist_append(pHeaders, cHeader)
                next
                curl_easy_setopt(pCurl, CURLOPT_HTTPHEADER, pHeaders)
            ok
            
            # Set method and data
            switch cMethod
                on "POST"
                    curl_easy_setopt(pCurl, CURLOPT_POST, 1)
                    if cData != ""
                        curl_easy_setopt(pCurl, CURLOPT_POSTFIELDS, cData)
                    ok
                on "PUT"
                    curl_easy_setopt(pCurl, CURLOPT_CUSTOMREQUEST, "PUT")
                    if cData != ""
                        curl_easy_setopt(pCurl, CURLOPT_POSTFIELDS, cData)
                    ok
                on "DELETE"
                    curl_easy_setopt(pCurl, CURLOPT_CUSTOMREQUEST, "DELETE")
                on "GET"
                    # GET is default, no special setup needed
            off
            
            # Set callback for response data
            curl_easy_setopt(pCurl, CURLOPT_WRITEFUNCTION, "writeCallback()")
            
            # Perform the request
            nResult = curl_easy_perform(pCurl)
            
            if nResult = CURLE_OK
                # Get response code
                curl_easy_getinfo(pCurl, CURLINFO_RESPONSE_CODE, :nStatusCode)
                oResponse[:status_code] = nStatusCode
                
                # Get response data
                oResponse[:data] = cResponseData
                oResponse[:success] = true
                
                # Clear any previous error
                oResponse[:error] = ""
            else
                oResponse[:error] = "Curl error: " + curl_easy_strerror(nResult)
            ok
            
            # Cleanup
            if pHeaders != NULL
                curl_slist_free_all(pHeaders)
            ok
            curl_easy_cleanup(pCurl)
            
        catch
            oResponse[:error] = "HTTP request failed: " + cCatchError
        done
        
        return oResponse



# Callback function for curl to write response data
func writeCallback cData, nSize, nMemb, pUserData
    
    cResponseData += cData
    return nSize * nMemb
