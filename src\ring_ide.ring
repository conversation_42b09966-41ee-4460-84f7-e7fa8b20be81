# RingIDE Class - Main IDE class that orchestrates all components
# Instantiates all service classes and manages the application lifecycle
# Entry point for the complete Maestro IDE system

load "webview.ring"
load "http_client.ring"
load "file_manager.ring"
load "code_runner.ring"
load "context_engine.ring"
load "ai_client.ring"
load "webview_handler.ring"

class RingIDE

    oWebView = null
    oHTTPClient = null
    oFileManager = null
    oCodeRunner = null
    oContextEngine = null
    oAIClient = null
    oWebViewHandler = null
    cUIPath = "ui/index.html"
    nWindowWidth = 1400
    nWindowHeight = 900
    cWindowTitle = "Maestro - Ring Language IDE"
    bDebugMode = false

    # Constructor
    func init
        ? "Initializing Maestro IDE..."
        initializeServices()
        ? "All services initialized successfully."
    
    # Initialize all service classes
    func initializeServices
        try
            # Initialize HTTP Client
            ? "Initializing HTTP Client..."
            oHTTPClient = new HTTPClient()
            oHTTPClient.setTimeout(30)
            
            # Initialize File Manager
            ? "Initializing File Manager..."
            oFileManager = new FileManager()
            
            # Initialize Code Runner
            ? "Initializing Code Runner..."
            oCodeRunner = new CodeRunner()
            oCodeRunner.setExecutionTimeout(30)
            
            # Initialize Context Engine
            ? "Initializing Context Engine..."
            oContextEngine = new ContextEngine()
            
            # Initialize AI Client
            ? "Initializing AI Client..."
            oAIClient = new AIClient(oHTTPClient)
            
            ? "All services initialized successfully."
            
        catch
            ? "Error initializing services: " + cCatchError
            shutdown()
        done
    
    # Start the IDE application
    func start
        try
            ? "Starting Maestro IDE..."
            
            # Check if UI file exists
            if not fexists(cUIPath)
                ? "Error: UI file not found at " + cUIPath
                ? "Please ensure the ui/index.html file exists."
                return
            ok
            
            # Initialize WebView
            ? "Initializing WebView..."
            oWebView = new WebView()
            oWebView.setTitle(cWindowTitle)
            oWebView.setSize(nWindowWidth,nWindowHeight, WEBVIEW_HINT_NONE) 
            
            # Enable debug mode if needed
            if bDebugMode
                setDebugMode(false)
            ok
            
            
            # Initialize WebView Handler with all services
            ? "Initializing WebView Handler..."
            oWebViewHandler = new WebViewHandler(
                oWebView,
                oFileManager,
                oCodeRunner,
                oAIClient,
                oContextEngine
            )
            
            ? "Maestro IDE started successfully!"
            ? "Window Title: " + cWindowTitle
            ? "Window Size: " + string(nWindowWidth) + "x" + string(nWindowHeight)
            ? "UI Path: " + cUIPath
            ? "Working Directory: " + CurrentDir()
            
            oWebView.navigate("file://" + CurrentDir() + "/" + cUIPath)
            # Start the WebView application loop
            oWebView.run()
            
        catch
            ? "Error starting IDE: " + cCatchError
            shutdown()
        done
    
    # Set window properties
    func setWindowProperties cTitle, nWidth, nHeight
        if cTitle != ""
            cWindowTitle = cTitle
        ok
        
        if nWidth > 0
            nWindowWidth = nWidth
        ok
        
        if nHeight > 0
            nWindowHeight = nHeight
        ok
    
    # Set UI file path
    func setUIPath cPath
        if fexists(cPath)
            cUIPath = cPath
            return true
        else
            ? "Warning: UI file not found at " + cPath
            return false
        ok
    
    # Enable or disable debug mode
    func setDebugMode bEnabled
        bDebugMode = bEnabled
        if bDebugMode
            ? "Debug mode enabled"
        else
            ? "Debug mode disabled"
        ok
    
    # Get application status
    func getStatus
        oStatus = [
            :appName = "Maestro IDE",
            :version = "1.0.0",
            :windowTitle = cWindowTitle,
            :windowSize = [nWindowWidth, nWindowHeight],
            :uiPath = cUIPath,
            :debugMode = bDebugMode,
            :workingDirectory = CurrentDir(),
            :servicesInitialized = (oHTTPClient != null and oFileManager != null and 
                                   oCodeRunner != null and oContextEngine != null and 
                                   oAIClient != null),
            :webViewInitialized = (oWebView != null),
            :handlerInitialized = (oWebViewHandler != null)
        ]
        
        return oStatus
    
    # Shutdown the IDE gracefully
    func shutdown
        try
            ? "Shutting down Maestro IDE..."
            
            # Clean up services
            if oWebViewHandler != null
                ? "Cleaning up WebView Handler..."
                oWebViewHandler = null
            ok
            
            if oWebView != null
                ? "Cleaning up WebView..."
                oWebView = null
            ok
            
            if oAIClient != null
                ? "Cleaning up AI Client..."
                oAIClient = null
            ok
            
            if oContextEngine != null
                ? "Cleaning up Context Engine..."
                oContextEngine = null
            ok
            
            if oCodeRunner != null
                ? "Cleaning up Code Runner..."
                oCodeRunner = null
            ok
            
            if oFileManager != null
                ? "Cleaning up File Manager..."
                oFileManager = null
            ok
            
            if oHTTPClient != null
                ? "Cleaning up HTTP Client..."
                oHTTPClient = null
            ok
            
            ? "Maestro IDE shutdown complete."
            
        catch
            ? "Error during shutdown: " + cCatchError
        done
    
    # Handle application errors
    func handleError cErrorMessage, cContext
        cFullError = "Error in " + cContext + ": " + cErrorMessage
        ? cFullError
        
        # Try to show error in UI if WebView is available
        if oWebViewHandler != null
            oWebViewHandler.showNotification(cFullError, "error")
        ok
        
        # Log error to file
        logError(cFullError)
    
    # Log error to file
    func logError cErrorMessage
        try
            cLogFile = "maestro_errors.log"
            cTimestamp = date() + " " + time()
            cLogEntry = cTimestamp + " - " + cErrorMessage + nl
            
            if fexists(cLogFile)
                cExistingLog = read(cLogFile)
                write(cLogFile, cExistingLog + cLogEntry)
            else
                write(cLogFile, cLogEntry)
            ok
            
        catch
            ? "Failed to log error: " + cCatchError
        done
    
    # Get service instances (for advanced usage)
    func getHTTPClient
        return oHTTPClient
    
    func getFileManager
        return oFileManager
    
    func getCodeRunner
        return oCodeRunner
    
    func getContextEngine
        return oContextEngine
    
    func getAIClient
        return oAIClient
    
    func getWebViewHandler
        return oWebViewHandler


        