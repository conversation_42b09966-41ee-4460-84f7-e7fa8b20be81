# WebViewHandler Class - Critical bridge between Ring and JavaScript
# Manages all WebView bindings and ensures proper communication protocol
# Wraps service method calls with unified error handling

load "jsonlib.ring"

class WebViewHandler

    oWebView = null
    oFileManager = null
    oCodeRunner = null
    oAIClient = null
    oContextEngine = null
    aBindList  = []    

    # Constructor - receives WebView instance and all service classes
    func init oWebViewInstance, oFileManagerInstance, oCodeRunnerInstance, oAIClientInstance, oContextEngineInstance
        oWebView = oWebViewInstance
        oFileManager = oFileManagerInstance
        oCodeRunner = oCodeRunnerInstance
        oAIClient = oAIClientInstance
        oContextEngine = oContextEngineInstance
        
        populateBindings()
        bindAllMethods()
    
    # Populate the definitive bindings list
    func populateBindings
        aBindList = [
                    # System bindings
                    [self,[
                        ["js_getInitialState", :getInitialState],
                        ["js_sendChatRequest", :handleChatRequest],
                        ["js_getSystemInfo", :getSystemInfo]
                    ]],
                    # File Manager bindings
                    [oFileManager,[
                        ["js_createNewFile",  :createFile],
                        ["js_readFile", :readFile],
                        ["js_writeFile", :writeFile],
                        ["js_deleteFile", :deleteFile],
                        ["js_listFiles", :listFiles],
                        ["js_getRecentFiles", :getRecentFiles],
                        ["js_fileExists", :fileExists],
                        ["js_setWorkingDirectory", :setWorkingDirectory],
                        ["js_getWorkingDirectory", :getWorkingDirectory]
                    ]],
                    # Code Runner bindings
                    [oCodeRunner,[
                        ["js_executeCode", :executeCode],
                        ["js_executeFile", :executeFile],
                        ["js_checkSyntax", :checkSyntax],
                        ["js_getExecutionHistory", :getExecutionHistory],
                        ["js_clearExecutionHistory", :clearExecutionHistory],
                        ["js_setExecutionTimeout", :setExecutionTimeout]
                    ]],
                    # AI Client bindings
                    [oAIClient,[
                        ["js_getAvailableProviders", :getAvailableProviders],
                        ["js_setDefaultProvider", :setDefaultProvider]
                    ]],
                    # Context Engine bindings
                    [oContextEngine,[
                        ["js_addMessage",  :addMessage],
                        ["js_getConversationHistory", :getConversationHistory],
                        ["js_clearChatHistory", :clearConversationHistory],
                        ["js_buildRequestContext", :buildRequestContext],
                        ["js_getContextStatistics", :getContextStatistics],
                        ["js_setContextParameters", :setContextParameters]
                    ]]
                ]
    
    # Bind all methods to WebView
    func bindAllMethods
       oWebView.bindMany(aBindList)
    
    # Handle chat request with context building
    func handleChatRequest id, req
        oFinalResponse = []
        
        try
            # Parse the JSON request
            aParams = json2list(req)
            
            # Extract parameters
            cProvider = ""
            cMessage = ""
            bUseContext = true
            
            if len(aParams) > 0
                cMessage = aParams[1]
            ok
            if len(aParams) > 1
                cProvider = aParams[2]
            ok
            if len(aParams) > 2
                bUseContext = aParams[3]
            ok
            
            # Build context if requested
            aContext = []
            if bUseContext
                oContextResult = oContextEngine.buildRequestContext([cMessage])
                if oContextResult[:success]
                    aContext = [oContextResult[:data][:context]]
                ok
            ok
            
            # Send request to AI
            oAIResult = oAIClient.sendChatRequest(cProvider, cMessage, aContext)
            
            if oAIResult[:success]
                # Add user message to history
                oContextEngine.addMessage(["user", cMessage])
                
                # Add assistant response to history
                oContextEngine.addMessage(["assistant", oAIResult[:content]])
                
                oFinalResponse = [
                    :success = true,
                    :data = [
                        :response = oAIResult[:content],
                        :provider = cProvider,
                        :message = cMessage
                    ],
                    :message = "Chat request completed successfully"
                ]
            else
                oFinalResponse = [
                    :success = false,
                    :data = [:error = oAIResult[:error]],
                    :message = "Chat request failed: " + oAIResult[:error]
                ]
            ok
            
        catch
            oFinalResponse = [
                :success = false,
                :data = [],
                :message = "Error handling chat request: " + cCatchError
            ]
        done
        
        # Return response as JSON
        oWebView.wreturn(id, 0, list2json(oFinalResponse))
    
    # Get initial application state
    func getInitialState id, req
        oFinalResponse = []
        
        try
            # Get conversation history
            oHistoryResult = oContextEngine.getConversationHistory([])
            
            # Get available providers
            oProvidersResult = oAIClient.getAvailableProviders()
            
            # Get working directory
            oWorkingDirResult = oFileManager.getWorkingDirectory()
            
            # Get recent files
            oRecentFilesResult = oFileManager.getRecentFiles([])
            
            # Get context statistics
            oContextStatsResult = oContextEngine.getContextStatistics([])
            
            oFinalResponse = [
                :success = true,
                :data = [
                    :conversationHistory = oHistoryResult[:data],
                    :availableProviders = oProvidersResult[:data],
                    :workingDirectory = oWorkingDirResult[:data],
                    :recentFiles = oRecentFilesResult[:data],
                    :contextStatistics = oContextStatsResult[:data]
                ],
                :message = "Initial state retrieved successfully"
            ]
            
        catch
            oFinalResponse = [
                :success = false,
                :data = [],
                :message = "Error getting initial state: " + cCatchError
            ]
        done
        
        oWebView.wreturn(id, 0, list2json(oFinalResponse))
    
    # Get system information
    func getSystemInfo id, req
        oFinalResponse = []
        
        try
            oFinalResponse = [
                :success = true,
                :data = [
                    :appName = "Maestro IDE",
                    :version = "1.0.0",
                    :ringVersion = version(),
                    :platform = sysget("OS"),
                    :currentDirectory = CurrentDir(),
                    :timestamp = date() + " " + time()
                ],
                :message = "System information retrieved successfully"
            ]
            
        catch
            oFinalResponse = [
                :success = false,
                :data = [],
                :message = "Error getting system info: " + cCatchError
            ]
        done
        
        oWebView.wreturn(id, 0, list2json(oFinalResponse))
    
    # Update UI from Ring side
    func updateUI cJavaScriptCode
        try
            oWebView.evalJS(cJavaScriptCode)
        catch
            # Log error but don't crash
            ? "Error updating UI: " + cCatchError
        done
    
    # Show notification in UI
    func showNotification cMessage, cType
        cType = lower(cType)
        if cType = ""
            cType = "info"
        ok
        
        cJSCode = "showNotification('" + cMessage + "', '" + cType + "');"
        updateUI(cJSCode)
    
    # Update editor content
    func updateEditorContent cContent
        # Escape content for JavaScript
        cEscapedContent = substr(cContent, "'", "\\'")
        cEscapedContent = substr(cEscapedContent, nl, "\\n")
        
        cJSCode = "updateEditorContent('" + cEscapedContent + "');"
        updateUI(cJSCode)
    
    # Update chat panel
    func updateChatPanel cMessage, cRole
        cEscapedMessage = substr(cMessage, "'", "\\'")
        cEscapedMessage = substr(cEscapedMessage, nl, "\\n")
        
        cJSCode = "addChatMessage('" + cEscapedMessage + "', '" + cRole + "');"
        updateUI(cJSCode)

