# CodeRunner Class - Headless Ring code execution manager
# Provides safe code execution with output capture and error handling
# All methods return standardized Ring list responses

class CodeRunner

    # Constructor
    func init
        cTempDir = CurrentDir() + "/temp"
        # Create temp directory if it doesn't exist
        if not direxists(cTempDir)
            system("mkdir " + cTempDir)
        ok
        aExecutionHistory = []
    
    # Set execution timeout
    func setExecutionTimeout nSeconds
        if nSeconds > 0
            nExecutionTimeout = nSeconds
            return [:success = true, :data = [:timeout = nSeconds], :message = "Execution timeout set successfully"]
        else
            return [:success = false, :data = [], :message = "Invalid timeout value"]
        ok
    
    # Execute Ring code
    func executeCode aParams
        try
            cCode = aParams[1]
            cFileName = ""
            
            # Optional filename parameter
            if len(aParams) > 1
                cFileName = aParams[2]
            else
                cFileName = "temp_" + string(clock()) + ".ring"
            ok
            
            # Create temporary file
            cTempFile = cTempDir + "/" + cFileName
            write(cTempFile, cCode)
            
            # Prepare execution command
            cCommand = "ring " + cTempFile
            
            # Execute code and capture output
            oResult = executeCommand(cCommand)
            
            # Clean up temporary file
            if fexists(cTempFile)
                remove(cTempFile)
            ok
            
            # Add to execution history
            addToExecutionHistory(cCode, oResult)
            
            if oResult[:success]
                return [
                    :success = true,
                    :data = [
                        :code = cCode,
                        :output = oResult[:output],
                        :executionTime = oResult[:executionTime],
                        :fileName = cFileName
                    ],
                    :message = "Code executed successfully"
                ]
            else
                return [
                    :success = false,
                    :data = [
                        :code = cCode,
                        :error = oResult[:error],
                        :output = oResult[:output],
                        :fileName = cFileName
                    ],
                    :message = "Code execution failed"
                ]
            ok
            
        catch
            return [:success = false, :data = [], :message = "Error executing code: " + cCatchError]
        done
    
    # Execute Ring file
    func executeFile aParams
        try
            cFilePath = aParams[1]
            
            # Check if file exists
            if not fexists(cFilePath)
                return [:success = false, :data = [], :message = "File not found: " + cFilePath]
            ok
            
            # Read file content
            cCode = read(cFilePath)
            
            # Prepare execution command
            cCommand = "ring " + cFilePath
            
            # Execute file and capture output
            oResult = executeCommand(cCommand)
            
            # Add to execution history
            addToExecutionHistory(cCode, oResult)
            
            if oResult[:success]
                return [
                    :success = true,
                    :data = [
                        :filePath = cFilePath,
                        :output = oResult[:output],
                        :executionTime = oResult[:executionTime]
                    ],
                    :message = "File executed successfully"
                ]
            else
                return [
                    :success = false,
                    :data = [
                        :filePath = cFilePath,
                        :error = oResult[:error],
                        :output = oResult[:output]
                    ],
                    :message = "File execution failed"
                ]
            ok
            
        catch
            return [:success = false, :data = [], :message = "Error executing file: " + cCatchError]
        done
    
    # Check Ring syntax
    func checkSyntax aParams
        try
            cCode = aParams[1]
            
            # Create temporary file
            cTempFile = cTempDir + "/syntax_check_" + string(clock()) + ".ring"
            write(cTempFile, cCode)
            
            # Use Ring's syntax checker
            cCommand = "ring -c " + cTempFile
            oResult = executeCommand(cCommand)
            
            # Clean up temporary file
            if fexists(cTempFile)
                remove(cTempFile)
            ok
            
            if oResult[:success]
                return [
                    :success = true,
                    :data = [:code = cCode, :valid = true],
                    :message = "Syntax is valid"
                ]
            else
                return [
                    :success = false,
                    :data = [:code = cCode, :valid = false, :error = oResult[:error]],
                    :message = "Syntax error found"
                ]
            ok
            
        catch
            return [:success = false, :data = [], :message = "Error checking syntax: " + cCatchError]
        done
    
    # Get execution history
    func getExecutionHistory aParams
        nLimit = nMaxHistoryEntries
        if len(aParams) > 0 and aParams[1] > 0
            nLimit = aParams[1]
        ok
        
        aLimitedHistory = []
        nCount = min(len(aExecutionHistory), nLimit)
        
        for i = 1 to nCount
            aLimitedHistory + aExecutionHistory[i]
        next
        
        return [
            :success = true,
            :data = [:history = aLimitedHistory, :count = len(aLimitedHistory)],
            :message = "Execution history retrieved successfully"
        ]
    
    # Clear execution history
    func clearExecutionHistory aParams
        aExecutionHistory = []
        return [
            :success = true,
            :data = [],
            :message = "Execution history cleared successfully"
        ]
    
    
    
    
    private
    # Private attributes
        cTempDir = ""
        nExecutionTimeout = 30  # seconds
        aExecutionHistory = []
        nMaxHistoryEntries = 50

    # Private helper methods

    # Execute system command with timeout and output capture
    func executeCommand cCommand
        oResult = [
            :success = false,
            :output = "",
            :error = "",
            :executionTime = 0
        ]
        
        try
            nStartTime = clock()
            
            # Create output files
            cOutputFile = cTempDir + "/output_" + string(clock()) + ".txt"
            cErrorFile = cTempDir + "/error_" + string(clock()) + ".txt"
            
            # Modify command to redirect output
            cFullCommand = cCommand + " > " + cOutputFile + " 2> " + cErrorFile
            
            # Execute command
            nExitCode = system(cFullCommand)
            
            nEndTime = clock()
            oResult[:executionTime] = nEndTime - nStartTime
            
            # Read output files
            if fexists(cOutputFile)
                oResult[:output] = read(cOutputFile)
                remove(cOutputFile)
            ok
            
            if fexists(cErrorFile)
                oResult[:error] = read(cErrorFile)
                remove(cErrorFile)
            ok
            
            # Determine success based on exit code
            oResult[:success] = (nExitCode = 0)
            
        catch
            oResult[:error] = "Command execution failed: " + cCatchError
        done
        
        return oResult
    
    # Add execution to history
    func addToExecutionHistory cCode, oResult
        aHistoryEntry = [
            :timestamp = date() + " " + time(),
            :code = cCode,
            :output = oResult[:output],
            :error = oResult[:error],
            :success = oResult[:success],
            :executionTime = oResult[:executionTime]
        ]
        
        # Add to beginning of history
        aExecutionHistory = [aHistoryEntry] + aExecutionHistory
        
        # Limit history size
        if len(aExecutionHistory) > nMaxHistoryEntries
            aExecutionHistory = sublist(aExecutionHistory, 1, nMaxHistoryEntries)
        ok
