# Test Example for Maestro IDE
# This file demonstrates Ring language features and can be used to test the IDE

# Basic output
? "Welcome to Maestro IDE!"
? "Testing Ring language features..."

# Variables with Hungarian notation
cName = "Ring Developer"
nAge = 25
bIsActive = true
aLanguages = ["Ring", "Python", "JavaScript", "C++"]

# Display variables
? "Name: " + cName
? "Age: " + string(nAge)
? "Active: " + string(bIsActive)
? "Languages: " + list2str(aLanguages)


# Call function
cResult = greetUser(cName)
? cResult


# Create developer object
oDev = new Developer("Ahmed", 3)
oDev.addSkill("Ring")
oDev.addSkill("Web Development")
oDev.addSkill("AI Integration")
oDev.displayInfo()

# Control structures
? "Testing control structures..."

# If statement
if nAge >= 18
    ? "You are an adult"
else
    ? "You are a minor"
ok

# Switch statement
switch cName
    on "Ring Developer"
        ? "Welcome, Ring Developer!"
    on "<PERSON>"
        ? "Hello <PERSON>!"
    other
        ? "Hello, unknown user!"
off

# For loop
? "Counting from 1 to 5:"
for i = 1 to 5
    ? "Count: " + string(i)
next

# While loop
nCounter = 1
? "While loop example:"
while nCounter <= 3
    ? "Counter: " + string(nCounter)
    nCounter++
end

# For in loop
? "Programming languages:"
for cLang in aLanguages
    ? "- " + cLang
next

# Error handling
? "Testing error handling..."
try
    nResult = 10 / 0  # This will cause an error
catch
    ? "Error caught: " + cCatchError
done



aTestNumbers = [1, 2, 3, 4, 5]
nTotal = calculateSum(aTestNumbers)
? "Sum of numbers: " + string(nTotal)

# Hash/Dictionary example
oPersonInfo = [
    :name = "Sara",
    :age = 28,
    :city = "Cairo",
    :skills = ["Ring", "Database", "UI Design"]
]

? "Person Info:"
? "Name: " + oPersonInfo[:name]
? "Age: " + string(oPersonInfo[:age])
? "City: " + oPersonInfo[:city]

# Test completed
? "All tests completed successfully!"
? "Maestro IDE is ready for Ring development!"

# Function definition
func greetUser cUserName
    ? "Hello, " + cUserName + "! Welcome to Ring programming."
    return "Greeting sent to " + cUserName

# Function with parameters
func calculateSum aNumbers
    nSum = 0
    for nNum in aNumbers
        nSum += nNum
    next
    return nSum

# Class definition
class Developer
    # Public attributes
    cName = ""
    nExperience = 0
    aSkills = []
    
    # Constructor
    func init cDevName, nYears
        cName = cDevName
        nExperience = nYears
        aSkills = []
    
    # Add skill method
    func addSkill cSkill
        aSkills + cSkill
        ? cName + " learned " + cSkill
    
    # Display info method
    func displayInfo
        ? "Developer: " + cName
        ? "Experience: " + string(nExperience) + " years"
        ? "Skills: " + list2str(aSkills)
    
    private
    
    # Private method
    func calculateLevel
        if nExperience < 2
            return "Junior"
        elseif nExperience < 5
            return "Mid-level"
        else
            return "Senior"
        ok
