# Maestro - Ring Language IDE
# Main entry point for the application
# 
# This file serves as the single entry point for the Maestro IDE.
# It instantiates the RingIDE class and starts the application.
#
# Usage: ring main.ring
#
# Author: Maestro Development Team
# Version: 1.0.0

# Load the main IDE class
load "src/ring_ide.ring"

# Main function - entry point
func main
    # Display welcome message
    displayWelcomeMessage()
    
    # Check system requirements
    if not checkSystemRequirements()
        ? "System requirements not met. Exiting..."
        return
    ok
    
    # Create and start the IDE
    try
        # Instantiate the main IDE class
        oMaestroIDE = new RingIDE()
        
        # Configure IDE settings (optional)
        oMaestroIDE.setWindowProperties("Maestro - Ring Language IDE", 1400, 900)
        oMaestroIDE.setDebugMode(true)  # Set to true for debugging
        
        # Start the IDE application
        oMaestroIDE.start()
        
    catch
        ? "Fatal error starting Maestro IDE: " + cCatchError
        ? "Please check the error log for more details."
        
        # Try to log the error
        try
            cLogFile = "maestro_startup_errors.log"
            cTimestamp = date() + " " + time()
            cLogEntry = cTimestamp + " - Fatal startup error: " + cCatchError + nl
            
            if fexists(cLogFile)
                cExistingLog = read(cLogFile)
                write(cLogFile, cExistingLog + cLogEntry)
            else
                write(cLogFile, cLogEntry)
            ok
            
            ? "Error logged to: " + cLogFile
            
        catch
            ? "Failed to log startup error."
        done
    done

# Display welcome message
func displayWelcomeMessage
    ? "========================================"
    ? "    Maestro - Ring Language IDE"
    ? "========================================"
    ? "Version: 1.0.0"
    ? "Platform: " + sysget("OS")
    ? "Ring Version: " + version()
    ? "Working Directory: " + CurrentDir()
    ? "========================================"
    ? ""

# Check system requirements
func checkSystemRequirements
    ? "Checking system requirements..."
    
    # Check Ring version
    cRingVersion = version()
    ? "Ring Version: " + cRingVersion
    
    # Check for required directories
    if not direxists("src")
        ? "Error: 'src' directory not found!"
        ? "Please ensure all source files are in the 'src' directory."
        return false
    ok
    
    if not direxists("ui")
        ? "Error: 'ui' directory not found!"
        ? "Please ensure the user interface files are in the 'ui' directory."
        return false
    ok
    
    if not direxists("config")
        ? "Warning: 'config' directory not found. It will be created automatically."
    ok
    
    # Check for required source files
    aRequiredFiles = [
        "src/ring_ide.ring",
        "src/webview_handler.ring",
        "src/http_client.ring",
        "src/ai_client.ring",
        "src/context_engine.ring",
        "src/file_manager.ring",
        "src/code_runner.ring"
    ]
    
    for cFile in aRequiredFiles
        if not fexists(cFile)
            ? "Error: Required file not found: " + cFile
            return false
        ok
    next
    
    # Check for UI file
    if not fexists("ui/index.html")
        ? "Error: UI file not found: ui/index.html"
        ? "Please ensure the user interface file exists."
        return false
    ok
    
    # Check WebView library availability
    try
        load "webview.ring"
        ? "WebView library: Available"
    catch
        ? "Error: WebView library not available!"
        ? "Please ensure the WebView library is properly installed."
        return false
    done
    
    # Check libcurl availability
    try
        load "libcurl.ring"
        ? "LibCurl library: Available"
    catch
        ? "Warning: LibCurl library not available!"
        ? "HTTP functionality may be limited."
        # Don't return false here as the app can still work without HTTP
    done
    
    # Check JSON library availability
    try
        load "jsonlib.ring"
        ? "JSON library: Available"
    catch
        ? "Error: JSON library not available!"
        ? "JSON functionality is required for the application."
        return false
    done
    
    ? "System requirements check completed successfully."
    ? ""
    return true

# Handle command line arguments (future enhancement)
func handleCommandLineArgs
    # This function can be expanded to handle command line arguments
    # such as --debug, --config-path, --ui-path, etc.
    
    # For now, just return default settings
    return [
        :debug = false,
        :configPath = "config/api_keys.json",
        :uiPath = "ui/index.html"
    ]


