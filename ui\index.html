<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maestro - Ring Language IDE</title>
    
    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/material-darker.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.css">
    
    <!-- Marked.js for Markdown rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    
    <!-- Prism.js for syntax highlighting in chat -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        /* Glassmorphism Theme with Dark Mode */
        :root {
            --primary-bg: #0a0a0a;
            --secondary-bg: rgba(255, 255, 255, 0.05);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --accent-color: #00d4ff;
            --accent-hover: #00b8e6;
            --success-color: #00ff88;
            --error-color: #ff4757;
            --warning-color: #ffa502;
            --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            --blur: blur(10px);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            backdrop-filter: var(--blur);
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--glass-bg);
            backdrop-filter: var(--blur);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            box-shadow: var(--shadow);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 20px;
            font-weight: bold;
            color: var(--accent-color);
        }
        
        .header-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            background: var(--glass-bg);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: var(--blur);
            border: 1px solid var(--glass-border);
        }
        
        .btn:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn.primary {
            background: var(--accent-color);
            color: #000;
        }
        
        .btn.primary:hover {
            background: var(--accent-hover);
        }
        
        /* Main Content */
        .main-content {
            display: flex;
            width: 100%;
            height: calc(100vh - 60px);
            margin-top: 60px;
        }
        
        /* Sidebar */
        .sidebar {
            width: 250px;
            background: var(--glass-bg);
            backdrop-filter: var(--blur);
            border-right: 1px solid var(--glass-border);
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow);
        }
        
        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid var(--glass-border);
            font-weight: bold;
            color: var(--accent-color);
        }
        
        .file-explorer {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }
        
        .file-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-item:hover {
            background: var(--glass-bg);
            transform: translateX(5px);
        }
        
        .file-item.active {
            background: var(--accent-color);
            color: #000;
        }
        
        /* Editor Area */
        .editor-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--secondary-bg);
        }
        
        .editor-tabs {
            display: flex;
            background: var(--glass-bg);
            border-bottom: 1px solid var(--glass-border);
            padding: 0 10px;
        }
        
        .editor-tab {
            padding: 10px 20px;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
        }
        
        .editor-tab.active {
            background: var(--accent-color);
            color: #000;
        }
        
        .editor-wrapper {
            flex: 1;
            position: relative;
        }
        
        #editor {
            height: 100%;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }
        
        /* Chat Panel */
        .chat-panel {
            width: 350px;
            background: var(--glass-bg);
            backdrop-filter: var(--blur);
            border-left: 1px solid var(--glass-border);
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow);
        }
        
        .chat-header {
            padding: 15px;
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-title {
            font-weight: bold;
            color: var(--accent-color);
        }
        
        .chat-controls {
            display: flex;
            gap: 5px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .message {
            padding: 12px;
            border-radius: 12px;
            max-width: 90%;
            word-wrap: break-word;
            backdrop-filter: var(--blur);
            border: 1px solid var(--glass-border);
        }
        
        .message.user {
            background: var(--accent-color);
            color: #000;
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.assistant {
            background: var(--glass-bg);
            color: var(--text-primary);
            align-self: flex-start;
        }
        
        .chat-input-container {
            padding: 15px;
            border-top: 1px solid var(--glass-border);
        }
        
        .chat-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            background: var(--glass-bg);
            color: var(--text-primary);
            backdrop-filter: var(--blur);
            resize: vertical;
            min-height: 40px;
            max-height: 120px;
        }
        
        .chat-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }
        
        /* Status Bar */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: var(--glass-bg);
            backdrop-filter: var(--blur);
            border-top: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        /* Notifications */
        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            backdrop-filter: var(--blur);
            border: 1px solid var(--glass-border);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: var(--success-color);
            color: #000;
        }
        
        .notification.error {
            background: var(--error-color);
            color: #fff;
        }
        
        .notification.warning {
            background: var(--warning-color);
            color: #000;
        }
        
        .notification.info {
            background: var(--glass-bg);
            color: var(--text-primary);
        }
        
        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--secondary-bg);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--glass-border);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent-color);
        }
        
        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--glass-border);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Hidden class */
        .hidden {
            display: none !important;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            
            .chat-panel {
                width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- App Container -->
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span>🎼</span>
                <span>Maestro IDE</span>
            </div>
            <div class="header-controls">
                <button class="btn" onclick="newFile()">New</button>
                <button class="btn" onclick="openFile()">Open</button>
                <button class="btn" onclick="saveFile()">Save</button>
                <button class="btn" onclick="runCode()">Run</button>
                <button class="btn" onclick="toggleTheme()">Theme</button>
                <button class="btn" onclick="showSettings()">Settings</button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">File Explorer</div>
                <div class="file-explorer" id="fileExplorer">
                    <!-- Files will be populated here -->
                </div>
            </div>

            <!-- Editor Container -->
            <div class="editor-container">
                <div class="editor-tabs" id="editorTabs">
                    <div class="editor-tab active" data-file="untitled.ring">untitled.ring</div>
                </div>
                <div class="editor-wrapper">
                    <textarea id="editor" placeholder="// Welcome to Maestro IDE
// Start typing your Ring code here...
// Use // gen at the end of a comment and press Ctrl+Enter to generate code from AI

# Example Ring code:
? 'Hello, World!'

# Define a function
func greetUser cName
    ? 'Hello, ' + cName + '!'

# Call the function
greetUser('Ring Developer')"></textarea>
                </div>
            </div>

            <!-- Chat Panel -->
            <div class="chat-panel">
                <div class="chat-header">
                    <div class="chat-title">AI Assistant</div>
                    <div class="chat-controls">
                        <button class="btn" onclick="newChat()" title="New Chat">🗨️</button>
                        <button class="btn" onclick="clearChat()" title="Clear Chat">🗑️</button>
                        <button class="btn" onclick="toggleChat()" title="Toggle Chat">📱</button>
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <strong>Maestro AI:</strong> Hello! I'm your Ring programming assistant. I can help you write, debug, and improve Ring code. Try typing a comment ending with <code>// gen</code> and press <strong>Ctrl+Enter</strong> to generate code!
                    </div>
                </div>
                <div class="chat-input-container">
                    <textarea class="chat-input" id="chatInput" placeholder="Ask me anything about Ring programming..." onkeydown="handleChatKeydown(event)"></textarea>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span id="statusFile">untitled.ring</span>
                <span id="statusPosition">Line 1, Col 1</span>
            </div>
            <div class="status-right">
                <span id="statusProvider">AI: Gemini</span>
                <span id="statusConnection">●</span>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Settings Modal (will be created dynamically) -->
    <div id="settingsModal" class="hidden"></div>

    <!-- CodeMirror JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/clike/clike.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/brace-fold.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>

    <script>
        // Global variables
        let editor;
        let currentFile = 'untitled.ring';
        let isDarkMode = true;
        let availableProviders = [];
        let currentProvider = 'gemini';
        let conversationHistory = [];
        let isGenerating = false;

        // Initialize the application
        window.onload = async function() {
            try {
                initializeEditor();
                await loadInitialState();
                setupEventListeners();
                showNotification('Maestro IDE loaded successfully!', 'success');
            } catch (error) {
                console.error('Error initializing app:', error);
                showNotification('Error initializing IDE: ' + error.message, 'error');
            }
        };

        // Initialize CodeMirror editor
        function initializeEditor() {
            const textarea = document.getElementById('editor');
            editor = CodeMirror.fromTextArea(textarea, {
                mode: 'text/x-csrc', // Closest to Ring syntax
                theme: 'material-darker',
                lineNumbers: true,
                autoCloseBrackets: true,
                matchBrackets: true,
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                styleActiveLine: true,
                indentUnit: 4,
                tabSize: 4,
                lineWrapping: true,
                extraKeys: {
                    "Ctrl-Enter": handleGenerateFromComment,
                    "Ctrl-S": saveFile,
                    "Ctrl-R": runCode,
                    "Ctrl-N": newFile,
                    "Ctrl-O": openFile,
                    "Ctrl-T": toggleChat,
                    "F11": function(cm) {
                        cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                    },
                    "Esc": function(cm) {
                        if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
                    }
                }
            });

            // Update status bar on cursor activity
            editor.on('cursorActivity', function() {
                const cursor = editor.getCursor();
                document.getElementById('statusPosition').textContent =
                    `Line ${cursor.line + 1}, Col ${cursor.ch + 1}`;
            });

            // Auto-save functionality
            let saveTimeout;
            editor.on('change', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    // Auto-save logic here
                }, 2000);
            });
        }

        // Load initial application state
        async function loadInitialState() {
            try {
                const response = await window.js_getInitialState();
                if (response.success) {
                    const data = response.data;

                    // Load conversation history
                    if (data.conversationHistory && data.conversationHistory.history) {
                        conversationHistory = data.conversationHistory.history;
                        renderConversationHistory();
                    }

                    // Load available providers
                    if (data.availableProviders && data.availableProviders.providers) {
                        availableProviders = data.availableProviders.providers;
                        currentProvider = data.availableProviders.default_provider || 'gemini';
                        updateProviderStatus();
                    }

                    // Load recent files
                    if (data.recentFiles && data.recentFiles.files) {
                        updateFileExplorer(data.recentFiles.files);
                    }

                } else {
                    console.warn('Failed to load initial state:', response.message);
                }
            } catch (error) {
                console.error('Error loading initial state:', error);
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            saveFile();
                            break;
                        case 'r':
                            e.preventDefault();
                            runCode();
                            break;
                        case 'n':
                            e.preventDefault();
                            newFile();
                            break;
                        case 'o':
                            e.preventDefault();
                            openFile();
                            break;
                        case 't':
                            e.preventDefault();
                            toggleChat();
                            break;
                    }
                }
            });

            // Window resize handler
            window.addEventListener('resize', function() {
                if (editor) {
                    editor.refresh();
                }
            });
        }

        // File operations
        async function newFile() {
            const fileName = prompt('Enter file name:', 'untitled.ring');
            if (fileName) {
                try {
                    const response = await window.js_createNewFile(fileName, '');
                    if (response.success) {
                        currentFile = fileName;
                        editor.setValue('');
                        updateStatusFile(fileName);
                        showNotification('New file created: ' + fileName, 'success');
                    } else {
                        showNotification('Error creating file: ' + response.message, 'error');
                    }
                } catch (error) {
                    showNotification('Error creating file: ' + error.message, 'error');
                }
            }
        }

        async function openFile() {
            const fileName = prompt('Enter file name to open:');
            if (fileName) {
                try {
                    const response = await window.js_readFile(fileName);
                    if (response.success) {
                        currentFile = fileName;
                        editor.setValue(response.data.content);
                        updateStatusFile(fileName);
                        showNotification('File opened: ' + fileName, 'success');
                    } else {
                        showNotification('Error opening file: ' + response.message, 'error');
                    }
                } catch (error) {
                    showNotification('Error opening file: ' + error.message, 'error');
                }
            }
        }

        async function saveFile() {
            try {
                const content = editor.getValue();
                const response = await window.js_writeFile(currentFile, content);
                if (response.success) {
                    showNotification('File saved: ' + currentFile, 'success');
                } else {
                    showNotification('Error saving file: ' + response.message, 'error');
                }
            } catch (error) {
                showNotification('Error saving file: ' + error.message, 'error');
            }
        }

        async function runCode() {
            try {
                const code = editor.getValue();
                if (!code.trim()) {
                    showNotification('No code to run', 'warning');
                    return;
                }

                showNotification('Running code...', 'info');
                const response = await window.js_executeCode(code);

                if (response.success) {
                    const output = response.data.output || 'Code executed successfully (no output)';
                    addChatMessage('Code Output:\n```\n' + output + '\n```', 'assistant');
                    showNotification('Code executed successfully', 'success');
                } else {
                    const error = response.data.error || response.message;
                    addChatMessage('Execution Error:\n```\n' + error + '\n```', 'assistant');
                    showNotification('Code execution failed', 'error');
                }
            } catch (error) {
                showNotification('Error running code: ' + error.message, 'error');
            }
        }

        // Generate code from comment
        async function handleGenerateFromComment() {
            try {
                const cursor = editor.getCursor();
                const line = editor.getLine(cursor.line);

                // Check if line ends with // gen
                if (line.trim().endsWith('// gen')) {
                    const comment = line.replace('// gen', '').trim();
                    if (comment.startsWith('//')) {
                        const request = comment.substring(2).trim();
                        await generateCodeFromComment(request, cursor.line);
                    }
                }
            } catch (error) {
                showNotification('Error generating code: ' + error.message, 'error');
            }
        }

        async function generateCodeFromComment(request, lineNumber) {
            try {
                isGenerating = true;
                showNotification('Generating code...', 'info');

                const fullRequest = `Generate Ring code for: ${request}`;
                const response = await window.js_sendChatRequest(fullRequest, currentProvider, true);

                if (response.success) {
                    const generatedCode = extractCodeFromResponse(response.data.response);
                    if (generatedCode) {
                        // Insert generated code after the comment line
                        const cursor = {line: lineNumber + 1, ch: 0};
                        editor.replaceRange('\n' + generatedCode + '\n', cursor);
                        showNotification('Code generated successfully!', 'success');
                    } else {
                        addChatMessage(response.data.response, 'assistant');
                        showNotification('AI response added to chat', 'info');
                    }
                } else {
                    showNotification('Error generating code: ' + response.message, 'error');
                }
            } catch (error) {
                showNotification('Error generating code: ' + error.message, 'error');
            } finally {
                isGenerating = false;
            }
        }

        // Extract code blocks from AI response
        function extractCodeFromResponse(response) {
            const codeBlockRegex = /```(?:ring|Ring)?\n?([\s\S]*?)```/;
            const match = response.match(codeBlockRegex);
            return match ? match[1].trim() : null;
        }

        // Chat functions
        async function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message || isGenerating) return;

            try {
                isGenerating = true;
                input.value = '';

                // Add user message to chat
                addChatMessage(message, 'user');

                // Show typing indicator
                const typingId = addChatMessage('Thinking...', 'assistant', true);

                // Send to AI
                const response = await window.js_sendChatRequest(message, currentProvider, true);

                // Remove typing indicator
                removeChatMessage(typingId);

                if (response.success) {
                    addChatMessage(response.data.response, 'assistant');
                } else {
                    addChatMessage('Error: ' + response.message, 'assistant');
                }

            } catch (error) {
                addChatMessage('Error: ' + error.message, 'assistant');
            } finally {
                isGenerating = false;
            }
        }

        function addChatMessage(content, role, isTemporary = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            messageDiv.id = messageId;
            messageDiv.className = `message ${role}`;

            if (isTemporary) {
                messageDiv.classList.add('temporary');
            }

            // Process markdown and add code block buttons
            const processedContent = processMessageContent(content, role);
            messageDiv.innerHTML = processedContent;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageId;
        }

        function removeChatMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        function processMessageContent(content, role) {
            if (role === 'user') {
                return `<strong>You:</strong> ${escapeHtml(content)}`;
            } else {
                // Process markdown for assistant messages
                let processed = marked.parse(content);

                // Add copy and insert buttons to code blocks
                processed = processed.replace(/<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g,
                    (match, attrs, code) => {
                        const escapedCode = escapeHtml(code);
                        return `
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <button class="btn code-btn" onclick="copyToClipboard('${escapedCode.replace(/'/g, "\\'")}')">Copy</button>
                                    <button class="btn code-btn" onclick="insertIntoEditor('${escapedCode.replace(/'/g, "\\'")}')">Insert</button>
                                </div>
                                <pre><code${attrs}>${escapedCode}</code></pre>
                            </div>
                        `;
                    }
                );

                return `<strong>Maestro AI:</strong> ${processed}`;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('Code copied to clipboard!', 'success');
            }).catch(() => {
                showNotification('Failed to copy code', 'error');
            });
        }

        function insertIntoEditor(code) {
            const cursor = editor.getCursor();
            editor.replaceRange(code, cursor);
            showNotification('Code inserted into editor!', 'success');
        }

        function handleChatKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendChatMessage();
            }
        }

        async function newChat() {
            try {
                const response = await window.js_clearChatHistory();
                if (response.success) {
                    conversationHistory = [];
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="message assistant">
                            <strong>Maestro AI:</strong> Hello! I'm your Ring programming assistant. How can I help you today?
                        </div>
                    `;
                    showNotification('New chat started', 'success');
                } else {
                    showNotification('Error starting new chat: ' + response.message, 'error');
                }
            } catch (error) {
                showNotification('Error starting new chat: ' + error.message, 'error');
            }
        }

        async function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                await newChat();
            }
        }

        function toggleChat() {
            const chatPanel = document.querySelector('.chat-panel');
            chatPanel.style.display = chatPanel.style.display === 'none' ? 'flex' : 'none';
            if (editor) {
                setTimeout(() => editor.refresh(), 100);
            }
        }

        // UI Helper functions
        function updateStatusFile(fileName) {
            document.getElementById('statusFile').textContent = fileName;
            document.querySelector('.editor-tab.active').textContent = fileName;
            document.querySelector('.editor-tab.active').setAttribute('data-file', fileName);
        }

        function updateProviderStatus() {
            document.getElementById('statusProvider').textContent = `AI: ${currentProvider}`;
        }

        function renderConversationHistory() {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = '';

            conversationHistory.forEach(msg => {
                addChatMessage(msg.content, msg.role);
            });

            if (conversationHistory.length === 0) {
                messagesContainer.innerHTML = `
                    <div class="message assistant">
                        <strong>Maestro AI:</strong> Hello! I'm your Ring programming assistant. How can I help you today?
                    </div>
                `;
            }
        }

        function updateFileExplorer(files) {
            const explorer = document.getElementById('fileExplorer');
            explorer.innerHTML = '';

            files.forEach(file => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';
                fileDiv.textContent = file;
                fileDiv.onclick = () => openFileFromExplorer(file);
                explorer.appendChild(fileDiv);
            });
        }

        async function openFileFromExplorer(fileName) {
            try {
                const response = await window.js_readFile(fileName);
                if (response.success) {
                    currentFile = fileName;
                    editor.setValue(response.data.content);
                    updateStatusFile(fileName);

                    // Update active file in explorer
                    document.querySelectorAll('.file-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    event.target.classList.add('active');

                    showNotification('File opened: ' + fileName, 'success');
                } else {
                    showNotification('Error opening file: ' + response.message, 'error');
                }
            } catch (error) {
                showNotification('Error opening file: ' + error.message, 'error');
            }
        }

        // Theme and settings
        function toggleTheme() {
            isDarkMode = !isDarkMode;
            const root = document.documentElement;

            if (isDarkMode) {
                root.style.setProperty('--primary-bg', '#0a0a0a');
                root.style.setProperty('--text-primary', '#ffffff');
                editor.setOption('theme', 'material-darker');
            } else {
                root.style.setProperty('--primary-bg', '#f5f5f5');
                root.style.setProperty('--text-primary', '#333333');
                editor.setOption('theme', 'default');
            }

            showNotification(`Switched to ${isDarkMode ? 'dark' : 'light'} theme`, 'info');
        }

        async function showSettings() {
            try {
                const response = await window.js_getAvailableProviders();
                if (response.success) {
                    availableProviders = response.data.providers;
                    createSettingsModal();
                } else {
                    showNotification('Error loading settings: ' + response.message, 'error');
                }
            } catch (error) {
                showNotification('Error loading settings: ' + error.message, 'error');
            }
        }

        function createSettingsModal() {
            const modal = document.getElementById('settingsModal');
            modal.className = 'settings-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Settings</h3>
                        <button class="btn" onclick="closeSettings()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="setting-group">
                            <label>AI Provider:</label>
                            <select id="providerSelect" onchange="changeProvider()">
                                ${availableProviders.map(provider =>
                                    `<option value="${provider.name}" ${provider.name === currentProvider ? 'selected' : ''}>
                                        ${provider.name} ${provider.enabled ? '' : '(Disabled)'}
                                    </option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>Theme:</label>
                            <button class="btn" onclick="toggleTheme()">
                                ${isDarkMode ? 'Switch to Light' : 'Switch to Dark'}
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-backdrop" onclick="closeSettings()"></div>
            `;
            modal.classList.remove('hidden');
        }

        function closeSettings() {
            document.getElementById('settingsModal').classList.add('hidden');
        }

        async function changeProvider() {
            const select = document.getElementById('providerSelect');
            const newProvider = select.value;

            try {
                const response = await window.js_setDefaultProvider(newProvider);
                if (response.success) {
                    currentProvider = newProvider;
                    updateProviderStatus();
                    showNotification(`Switched to ${newProvider}`, 'success');
                } else {
                    showNotification('Error changing provider: ' + response.message, 'error');
                    select.value = currentProvider; // Revert selection
                }
            } catch (error) {
                showNotification('Error changing provider: ' + error.message, 'error');
                select.value = currentProvider; // Revert selection
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            container.appendChild(notification);

            // Trigger animation
            setTimeout(() => notification.classList.add('show'), 100);

            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add CSS for settings modal
        const additionalCSS = `
            .settings-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .modal-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
            }

            .modal-content {
                background: var(--glass-bg);
                backdrop-filter: var(--blur);
                border: 1px solid var(--glass-border);
                border-radius: 12px;
                padding: 20px;
                min-width: 400px;
                max-width: 600px;
                position: relative;
                z-index: 2001;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid var(--glass-border);
            }

            .modal-header h3 {
                margin: 0;
                color: var(--accent-color);
            }

            .setting-group {
                margin-bottom: 15px;
            }

            .setting-group label {
                display: block;
                margin-bottom: 5px;
                color: var(--text-primary);
                font-weight: bold;
            }

            .setting-group select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid var(--glass-border);
                border-radius: 6px;
                background: var(--glass-bg);
                color: var(--text-primary);
                backdrop-filter: var(--blur);
            }

            .code-block-container {
                position: relative;
                margin: 10px 0;
            }

            .code-block-header {
                display: flex;
                gap: 5px;
                margin-bottom: 5px;
            }

            .code-btn {
                padding: 4px 8px;
                font-size: 12px;
            }
        `;

        // Inject additional CSS
        const style = document.createElement('style');
        style.textContent = additionalCSS;
        document.head.appendChild(style);
    </script>
</body>
</html>
