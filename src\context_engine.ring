# ContextEngine Class - Conversation context and history manager
# Manages conversation history and builds request context for AI interactions
# All methods return standardized Ring list responses

class ContextEngine

    # Constructor
    func init
        aConversationHistory = []
        cCurrentSessionId = generateSessionId()
        setupSystemPrompt()
    
    # Setup system prompt for Ring IDE assistant
    func setupSystemPrompt
        cSystemPrompt = "You are <PERSON><PERSON>, an intelligent programming assistant for the Ring programming language. " +
                       "You are integrated into a professional IDE and help developers write, debug, and improve Ring code. " +
                       "Key capabilities:" + nl +
                       "- Provide Ring language syntax help and best practices" + nl +
                       "- Generate Ring code from comments or descriptions" + nl +
                       "- Debug and fix Ring code issues" + nl +
                       "- Explain Ring concepts and features" + nl +
                       "- Suggest code improvements and optimizations" + nl +
                       "- Help with Ring libraries and frameworks" + nl + nl +
                       "Ring Language Guidelines:" + nl +
                       "- Use Hungarian notation for variables (c for strings, n for numbers, a for arrays, o for objects, b for booleans)" + nl +
                       "- Use proper Ring syntax with 'func' for functions and 'class' for classes" + nl +
                       "- Follow Ring's object-oriented patterns" + nl +
                       "- Use Ring's built-in functions and libraries when appropriate" + nl +
                       "- Provide clear, commented code examples" + nl + nl +
                       "Always respond in a helpful, professional manner and focus on Ring programming solutions."
    
    # Add message to conversation history
    func addMessage aParams
        try
            cRole = aParams[1]      # "user" or "assistant"
            cContent = aParams[2]   # message content
            cTimestamp = ""
            
            if len(aParams) > 2
                cTimestamp = aParams[3]
            else
                cTimestamp = date() + " " + time()
            ok
            
            # Create message object
            oMessage = [
                :role = cRole,
                :content = cContent,
                :timestamp = cTimestamp,
                :sessionId = cCurrentSessionId
            ]
            
            # Add to history
            aConversationHistory + oMessage
            
            # Trim history if too long
            trimHistory()
            
            return [
                :success = true,
                :data = [:message = oMessage, :historyCount = len(aConversationHistory)],
                :message = "Message added to conversation history"
            ]
            
        catch
            return [:success = false, :data = [], :message = "Error adding message: " + cCatchError]
        done
    
    # Get conversation history
    func getConversationHistory aParams
        nLimit = nMaxHistoryMessages
        if len(aParams) > 0 and aParams[1] > 0
            nLimit = aParams[1]
        ok
        
        nCount = min(len(aConversationHistory), nLimit)
        aLimitedHistory = []
        
        # Get the most recent messages
        nStart = max(1, len(aConversationHistory) - nCount + 1)
        for i = nStart to len(aConversationHistory)
            aLimitedHistory + aConversationHistory[i]
        next
        
        return [
            :success = true,
            :data = [
                :history = aLimitedHistory,
                :count = len(aLimitedHistory),
                :totalCount = len(aConversationHistory),
                :sessionId = cCurrentSessionId
            ],
            :message = "Conversation history retrieved successfully"
        ]
    
    # Build context for AI request
    func buildRequestContext aParams
        try
            cUserMessage = aParams[1]
            cAdditionalContext = ""
            
            if len(aParams) > 1
                cAdditionalContext = aParams[2]
            ok
            
            # Start with system prompt
            cContext = cSystemPrompt + nl + nl
            
            # Add additional context if provided
            if cAdditionalContext != ""
                cContext += "Additional Context:" + nl + cAdditionalContext + nl + nl
            ok
            
            # Add conversation history
            cContext += "Conversation History:" + nl
            
            # Get recent messages for context
            nRecentMessages = min(nMaxHistoryMessages, len(aConversationHistory))
            if nRecentMessages > 0
                nStart = max(1, len(aConversationHistory) - nRecentMessages + 1)
                for i = nStart to len(aConversationHistory)
                    oMessage = aConversationHistory[i]
                    cContext += oMessage[:role] + ": " + oMessage[:content] + nl
                next
            ok
            
            # Add current user message
            cContext += nl + "Current Request:" + nl + "user: " + cUserMessage
            
            # Trim context if too long
            if len(cContext) > nMaxContextLength
                cContext = right(cContext, nMaxContextLength)
                # Try to start from a complete message
                nNewlinePos = substr(cContext, nl)
                if nNewlinePos > 0
                    cContext = substr(cContext, nNewlinePos + 1)
                ok
            ok
            
            return [
                :success = true,
                :data = [
                    :context = cContext,
                    :contextLength = len(cContext),
                    :historyMessagesUsed = nRecentMessages,
                    :userMessage = cUserMessage
                ],
                :message = "Request context built successfully"
            ]
            
        catch
            return [:success = false, :data = [], :message = "Error building context: " + cCatchError]
        done
    
    # Clear conversation history
    func clearConversationHistory aParams
        aConversationHistory = []
        cCurrentSessionId = generateSessionId()
        
        return [
            :success = true,
            :data = [:sessionId = cCurrentSessionId],
            :message = "Conversation history cleared successfully"
        ]
    
    # Set system prompt
    func setSystemPrompt aParams
        try
            cNewPrompt = aParams[1]
            cSystemPrompt = cNewPrompt
            
            return [
                :success = true,
                :data = [:systemPrompt = cSystemPrompt],
                :message = "System prompt updated successfully"
            ]
            
        catch
            return [:success = false, :data = [], :message = "Error setting system prompt: " + cCatchError]
        done
    
    # Get system prompt
    func getSystemPrompt aParams
        return [
            :success = true,
            :data = [:systemPrompt = cSystemPrompt],
            :message = "System prompt retrieved successfully"
        ]
    
    # Set context parameters
    func setContextParameters aParams
        try
            if len(aParams) > 0 and aParams[1] > 0
                nMaxHistoryMessages = aParams[1]
            ok
            
            if len(aParams) > 1 and aParams[2] > 0
                nMaxContextLength = aParams[2]
            ok
            
            return [
                :success = true,
                :data = [
                    :maxHistoryMessages = nMaxHistoryMessages,
                    :maxContextLength = nMaxContextLength
                ],
                :message = "Context parameters updated successfully"
            ]
            
        catch
            return [:success = false, :data = [], :message = "Error setting context parameters: " + cCatchError]
        done
    
    # Get context statistics
    func getContextStatistics aParams
        nTotalMessages = len(aConversationHistory)
        nUserMessages = 0
        nAssistantMessages = 0
        
        for oMessage in aConversationHistory
            if oMessage[:role] = "user"
                nUserMessages++
            elseif oMessage[:role] = "assistant"
                nAssistantMessages++
            ok
        next
        
        return [
            :success = true,
            :data = [
                :totalMessages = nTotalMessages,
                :userMessages = nUserMessages,
                :assistantMessages = nAssistantMessages,
                :sessionId = cCurrentSessionId,
                :maxHistoryMessages = nMaxHistoryMessages,
                :maxContextLength = nMaxContextLength
            ],
            :message = "Context statistics retrieved successfully"
        ]
    
    
    
    private
    # Private attributes
        aConversationHistory = []
        cSystemPrompt = ""
        nMaxHistoryMessages = 10
        nMaxContextLength = 8000
        cCurrentSessionId = ""
    
    # Private helper methods
    
    # Trim conversation history to stay within limits
    func trimHistory
        while len(aConversationHistory) > nMaxHistoryMessages * 2
            del(aConversationHistory, 1)  # Remove oldest message
        end
    
    # Generate unique session ID
    func generateSessionId
        return "session_" + string(clock()) + "_" + string(random(9999))
