{"default_provider": "gemini", "providers": {"gemini": {"api_key": "your_gemini_api_key_here", "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", "enabled": true, "model_name": "gemini-pro", "description": "Google Gemini Pro - Advanced language model with strong coding capabilities", "max_tokens": 2048, "temperature": 0.7, "supports_streaming": false}, "claude": {"api_key": "your_claude_api_key_here", "endpoint": "https://api.anthropic.com/v1/messages", "enabled": false, "model_name": "claude-3-sonnet-20240229", "description": "Anthropic Claude 3 Sonnet - Excellent for code analysis and generation", "max_tokens": 2048, "temperature": 0.7, "supports_streaming": false}, "openrouter": {"api_key": "your_openrouter_api_key_here", "endpoint": "https://openrouter.ai/api/v1/chat/completions", "enabled": false, "model_name": "google/gemini-pro", "description": "OpenRouter - Access to multiple AI models through a single API", "max_tokens": 2048, "temperature": 0.7, "supports_streaming": false, "available_models": ["google/gemini-pro", "anthropic/claude-3-sonnet", "openai/gpt-4", "openai/gpt-3.5-turbo", "meta-llama/llama-2-70b-chat", "mistral<PERSON>/mistral-7b-instruct"]}}, "settings": {"request_timeout": 30, "max_retries": 3, "retry_delay": 1, "enable_logging": true, "log_requests": false, "log_responses": false, "cache_responses": false, "cache_duration": 3600}, "ui_settings": {"default_theme": "dark", "enable_syntax_highlighting": true, "enable_auto_completion": true, "enable_code_folding": true, "font_family": "Consolas, Monaco, 'Courier New', monospace", "font_size": 14, "tab_size": 4, "enable_word_wrap": true, "show_line_numbers": true, "show_minimap": true}, "ring_settings": {"enable_syntax_checking": true, "auto_format_code": true, "highlight_errors": true, "show_function_signatures": true, "enable_code_completion": true, "ring_executable_path": "ring", "default_file_extension": ".ring", "backup_files": true, "auto_save_interval": 300}, "ai_prompts": {"system_prompt": "You are <PERSON><PERSON>, an intelligent programming assistant for the Ring programming language. You help developers write, debug, and improve Ring code. Always provide clear, commented code examples and follow Ring's best practices including Hungarian notation for variables.", "code_generation_prompt": "Generate Ring code for the following requirement. Use proper Ring syntax, Hungarian notation for variables (c for strings, n for numbers, a for arrays, o for objects, b for booleans), and include helpful comments:", "code_review_prompt": "Review this Ring code and suggest improvements. Check for syntax errors, performance issues, and adherence to Ring best practices:", "debug_prompt": "Help debug this Ring code. Identify potential issues and suggest fixes:", "explain_prompt": "Explain this Ring code in detail, including what each part does and how it works:"}, "shortcuts": {"run_code": "Ctrl+R", "save_file": "Ctrl+S", "open_file": "Ctrl+O", "new_file": "Ctrl+N", "generate_from_comment": "Ctrl+Enter", "toggle_chat": "Ctrl+T", "clear_chat": "Ctrl+Shift+C", "toggle_theme": "Ctrl+Shift+T", "format_code": "Ctrl+Shift+F", "find_replace": "Ctrl+H"}, "version": "1.0.0", "last_updated": "2025-08-23", "configuration_notes": ["Replace 'your_*_api_key_here' with your actual API keys", "Enable only the providers you have API keys for", "Adjust temperature (0.0-1.0) to control response creativity", "Higher temperature = more creative, lower = more focused", "Max tokens controls the maximum response length", "Request timeout is in seconds", "Enable logging for debugging purposes"]}