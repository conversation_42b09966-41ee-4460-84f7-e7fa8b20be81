# AIClient Class - Multi-provider AI client for code assistance
# Configuration-driven AI client supporting <PERSON>, <PERSON>, and OpenRouter
# Provides unified interface for different AI providers

load "jsonlib.ring"

class AIClient

    # Constructor
    func init oHttpClientInstance
        oHTTPClient = oHttpClientInstance
        loadConfiguration()
    
    # Load configuration from JSON file
    func loadConfiguration
        try
            if not fexists(cConfigPath)
                createDefaultConfig()
            ok
            
            cConfigContent = read(cConfigPath)
            oConfig = json2list(cConfigContent)
            
            # Extract default provider and providers list
            if find(oConfig, :default_provider) > 0
                cDefaultProvider = oConfig[:default_provider]
            ok
            
            if find(oConfig, :providers) > 0
                aProviders = oConfig[:providers]
            ok
            
        catch
            # Create default config if loading fails
            createDefaultConfig()
        done
    
    # Create default configuration file
    func createDefaultConfig
        oDefaultConfig = [
            :default_provider = "gemini",
            :providers = [
                :gemini = [
                    :api_key = "your_gemini_api_key_here",
                    :endpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                    :enabled = true,
                    :model_name = "gemini-pro"
                ],
                :claude = [
                    :api_key = "your_claude_api_key_here", 
                    :endpoint = "https://api.anthropic.com/v1/messages",
                    :enabled = false,
                    :model_name = "claude-3-sonnet-20240229"
                ],
                :openrouter = [
                    :api_key = "your_openrouter_api_key_here",
                    :endpoint = "https://openrouter.ai/api/v1/chat/completions",
                    :enabled = false,
                    :model_name = "google/gemini-pro"
                ]
            ]
        ]
        
        # Create config directory if it doesn't exist
        if not dirExists("config")
            makedir("config")
        ok
        
        write(cConfigPath, list2json(oDefaultConfig))
        oConfig = oDefaultConfig
        aProviders = oConfig[:providers]
    
    # Send chat request using specified or default provider
    func sendChatRequest cProvider, cMessage, aContext
        try
            # Use default provider if none specified
            if cProvider = "" or cProvider = null
                cProvider = cDefaultProvider
            ok
            
            # Check if provider exists and is enabled
            if not find(aProviders, cProvider) > 0
                return [:success = false, :content = "", :error = "Provider not found: " + cProvider]
            ok
            
            oProviderConfig = aProviders[cProvider]
            if not oProviderConfig[:enabled]
                return [:success = false, :content = "", :error = "Provider is disabled: " + cProvider]
            ok
            
            # Route to appropriate provider method
            switch cProvider
                on "gemini"
                    return sendGeminiRequest(oProviderConfig, cMessage, aContext)
                on "claude"
                    return sendClaudeRequest(oProviderConfig, cMessage, aContext)
                on "openrouter"
                    return sendOpenRouterRequest(oProviderConfig, cMessage, aContext)
                other
                    return [:success = false, :content = "", :error = "Unsupported provider: " + cProvider]
            off
            
        catch
            return [:success = false, :content = "", :error = "Error in sendChatRequest: " + cCatchError]
        done
    
    # Get list of available providers
    func getAvailableProviders
        aAvailableProviders = []
        
        for cProviderName in keys(aProviders)
            oProvider = aProviders[cProviderName]
            aAvailableProviders + [
                :name = cProviderName,
                :enabled = oProvider[:enabled],
                :model_name = oProvider[:model_name]
            ]
        next
        
        return [
            :success = true,
            :data = [
                :providers = aAvailableProviders,
                :default_provider = cDefaultProvider,
                :count = len(aAvailableProviders)
            ],
            :message = "Available providers retrieved successfully"
        ]
    
    # Set default provider
    func setDefaultProvider cProvider
        if find(aProviders, cProvider) > 0
            cDefaultProvider = cProvider
            oConfig[:default_provider] = cProvider
            # Save updated config
            write(cConfigPath, list2json(oConfig))
            return [:success = true, :content = "", :error = ""]
        else
            return [:success = false, :content = "", :error = "Provider not found: " + cProvider]
        ok
    
    
    
    private
    # Private attributes
        oHTTPClient = null
        oConfig = null
        cConfigPath = "config/api_keys.json"
        cDefaultProvider = "gemini"
        aProviders = []

    # Private methods for each provider

    # Send request to Gemini API
    func sendGeminiRequest oProviderConfig, cMessage, aContext
        try
            # Prepare Gemini payload
            oPayload = _prepareGeminiPayload(cMessage, aContext)
            cPayloadJSON = list2json(oPayload)
            
            # Prepare headers
            aHeaders = [
                "Content-Type: application/json",
                "x-goog-api-key: " + oProviderConfig[:api_key]
            ]
            
            # Send request
            oResponse = oHTTPClient.postRequest(oProviderConfig[:endpoint], cPayloadJSON, aHeaders)
            
            if oResponse[:success]
                return _parseGeminiResponse(oResponse[:data])
            else
                return [:success = false, :content = "", :error = "HTTP request failed: " + oResponse[:error]]
            ok
            
        catch
            return [:success = false, :content = "", :error = "Gemini request error: " + cCatchError]
        done
    
    # Send request to Claude API
    func sendClaudeRequest oProviderConfig, cMessage, aContext
        try
            # Prepare Claude payload
            oPayload = _prepareClaudePayload(cMessage, aContext, oProviderConfig[:model_name])
            cPayloadJSON = list2json(oPayload)
            
            # Prepare headers
            aHeaders = [
                "Content-Type: application/json",
                "x-api-key: " + oProviderConfig[:api_key],
                "anthropic-version: 2023-06-01"
            ]
            
            # Send request
            oResponse = oHTTPClient.postRequest(oProviderConfig[:endpoint], cPayloadJSON, aHeaders)
            
            if oResponse[:success]
                return _parseClaudeResponse(oResponse[:data])
            else
                return [:success = false, :content = "", :error = "HTTP request failed: " + oResponse[:error]]
            ok
            
        catch
            return [:success = false, :content = "", :error = "Claude request error: " + cCatchError]
        done
    
    # Send request to OpenRouter API
    func sendOpenRouterRequest oProviderConfig, cMessage, aContext
        try
            # Prepare OpenRouter payload
            oPayload = _prepareOpenRouterPayload(cMessage, aContext, oProviderConfig[:model_name])
            cPayloadJSON = list2json(oPayload)
            
            # Prepare headers
            aHeaders = [
                "Content-Type: application/json",
                "Authorization: Bearer " + oProviderConfig[:api_key],
                "HTTP-Referer: https://maestro-ide.local",
                "X-Title: Maestro IDE"
            ]
            
            # Send request
            oResponse = oHTTPClient.postRequest(oProviderConfig[:endpoint], cPayloadJSON, aHeaders)
            
            if oResponse[:success]
                return _parseOpenRouterResponse(oResponse[:data])
            else
                return [:success = false, :content = "", :error = "HTTP request failed: " + oResponse[:error]]
            ok
            
        catch
            return [:success = false, :content = "", :error = "OpenRouter request error: " + cCatchError]
        done

    # Prepare Gemini API payload
    func _prepareGeminiPayload cMessage, aContext
        # Build the prompt with context
        cFullPrompt = ""
        if len(aContext) > 0
            cFullPrompt = aContext[1] + nl + nl + cMessage
        else
            cFullPrompt = cMessage
        ok

        return [
            :contents = [
                [
                    :parts = [
                        [:text = cFullPrompt]
                    ]
                ]
            ],
            :generationConfig = [
                :temperature = 0.7,
                :topK = 40,
                :topP = 0.95,
                :maxOutputTokens = 2048
            ]
        ]

    # Parse Gemini API response
    func _parseGeminiResponse cResponseData
        try
            oResponse = json2list(cResponseData)

            if find(oResponse, :candidates) > 0 and len(oResponse[:candidates]) > 0
                oCandidate = oResponse[:candidates][1]
                if find(oCandidate, :content) > 0 and find(oCandidate[:content], :parts) > 0
                    aParts = oCandidate[:content][:parts]
                    if len(aParts) > 0 and find(aParts[1], :text) > 0
                        cContent = aParts[1][:text]
                        return [:success = true, :content = cContent, :error = ""]
                    ok
                ok
            ok

            # Check for error in response
            if find(oResponse, :error) > 0
                cError = "Gemini API error: " + oResponse[:error][:message]
                return [:success = false, :content = "", :error = cError]
            ok

            return [:success = false, :content = "", :error = "Invalid Gemini response format"]

        catch
            return [:success = false, :content = "", :error = "Error parsing Gemini response: " + cCatchError]
        done

    # Prepare Claude API payload
    func _prepareClaudePayload cMessage, aContext, cModel
        aMessages = []

        # Add context as system message if provided
        cSystemMessage = ""
        if len(aContext) > 0
            cSystemMessage = aContext[1]
        ok

        # Add user message
        aMessages + [:role = "user", :content = cMessage]

        oPayload = [
            :model = cModel,
            :max_tokens = 2048,
            :temperature = 0.7,
            :messages = aMessages
        ]

        if cSystemMessage != ""
            oPayload[:system] = cSystemMessage
        ok

        return oPayload

    # Parse Claude API response
    func _parseClaudeResponse cResponseData
        try
            oResponse = json2list(cResponseData)

            if find(oResponse, :content) > 0 and len(oResponse[:content]) > 0
                oContent = oResponse[:content][1]
                if find(oContent, :text) > 0
                    cContent = oContent[:text]
                    return [:success = true, :content = cContent, :error = ""]
                ok
            ok

            # Check for error in response
            if find(oResponse, :error) > 0
                cError = "Claude API error: " + oResponse[:error][:message]
                return [:success = false, :content = "", :error = cError]
            ok

            return [:success = false, :content = "", :error = "Invalid Claude response format"]

        catch
            return [:success = false, :content = "", :error = "Error parsing Claude response: " + cCatchError]
        done

    # Prepare OpenRouter API payload
    func _prepareOpenRouterPayload cMessage, aContext, cModel
        aMessages = []

        # Add context as system message if provided
        if len(aContext) > 0
            aMessages + [:role = "system", :content = aContext[1]]
        ok

        # Add user message
        aMessages + [:role = "user", :content = cMessage]

        return [
            :model = cModel,
            :messages = aMessages,
            :temperature = 0.7,
            :max_tokens = 2048,
            :top_p = 0.95
        ]

    # Parse OpenRouter API response
    func _parseOpenRouterResponse cResponseData
        try
            oResponse = json2list(cResponseData)

            if find(oResponse, :choices) > 0 and len(oResponse[:choices]) > 0
                oChoice = oResponse[:choices][1]
                if find(oChoice, :message) > 0 and find(oChoice[:message], :content) > 0
                    cContent = oChoice[:message][:content]
                    return [:success = true, :content = cContent, :error = ""]
                ok
            ok

            # Check for error in response
            if find(oResponse, :error) > 0
                cError = "OpenRouter API error: " + oResponse[:error][:message]
                return [:success = false, :content = "", :error = cError]
            ok

            return [:success = false, :content = "", :error = "Invalid OpenRouter response format"]

        catch
            return [:success = false, :content = "", :error = "Error parsing OpenRouter response: " + cCatchError]
        done
